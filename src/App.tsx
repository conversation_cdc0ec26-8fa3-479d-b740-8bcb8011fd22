import { HashRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import Home from './pages/Home';
import About from './pages/About';
import Contact from './pages/Contact';
import UsefulLinks from './pages/UsefulLinks';
import NotFound from './pages/NotFound';
import PopupModal from './components/PopupModal';

function App() {
  return (
    <Router>
      <div className="flex flex-col min-h-screen">
        <PopupModal />
        <Navbar />
        <main className="flex-grow">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/useful-links" element={<UsefulLinks />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;